import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDateString,
  IsObject,
  IsOptional,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { IOrder } from '@core/pricing/domain/order.interface';

export class GetAvailableServicesRequestDto {
  @ApiProperty({ example: '2023-01-01T12:00:00Z' })
  @IsDateString()
  pickupDate: string;

  @ApiProperty({
    description: 'Whether to include pricing calculations',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  includePricing?: boolean;

  @ApiProperty({
    description: 'Order details for price calculation',
    required: false,
  })
  @IsObject()
  @IsOptional()
  order?: any;
}

export class ServicePriceDto {
  @ApiProperty({ example: 25.99 })
  basePrice: number;

  @ApiProperty({ example: 30.49 })
  totalPrice: number;

  @ApiProperty({
    example: [{ name: 'Fuel Surcharge', amount: 4.5 }],
    required: false,
  })
  modifiers?: Array<{ name: string; amount: number }>;

  @ApiPropertyOptional({
    required : false
  })
  totalModifierPrice? : any
}

export class AvailableServiceDto {
  @ApiProperty({ example: '66fedab8-7820-4cca-b8ca-cf59ade1aada' })
  id: string;

  @ApiProperty({ example: 'VNP 4 hours Fuel Charges' })
  name: string;

  @ApiProperty()
  deliveryDate: Date;

  @ApiProperty({ required: false })
  pricing?: ServicePriceDto;
}

export class GetAvailableServicesResponseDto {
  @ApiProperty({ type: [AvailableServiceDto] })
  data: AvailableServiceDto[];
}
