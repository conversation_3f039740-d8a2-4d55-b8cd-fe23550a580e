import { ApiProperty } from '@nestjs/swagger';
import { AddressDto } from '../../../order/orders/dto/address.dto';

export class ServiceOptionDto {
  @ApiProperty({ example: 'Fuel Surcharge 15%' })
  description: string;
}

export class OrderDetailsResponseDto {
  @ApiProperty({ example: '795599' })
  orderNumber: string;

  @ApiProperty({ example: 'In Transit' })
  status: string;

  @ApiProperty({ example: 'Hubscher Ribbons' })
  customerName: string;

  @ApiProperty({ example: 'Hubscher Ribbons' })
  requestedBy: string;

  @ApiProperty({ example: '2024-05-28 11:43 AM' })
  collectionTime: string;

  @ApiProperty({ example: '2024-05-28 5:00 PM' })
  deliveryTime: string;

  @ApiProperty()
  collectionLocation: AddressDto;

  @ApiProperty()
  deliveryLocation: AddressDto;

  @ApiProperty({ example: 'Sameday' })
  serviceLevel: string;

  @ApiProperty({ type: [ServiceOptionDto] })
  serviceOptions: ServiceOptionDto[];

  @ApiProperty({ example: false })
  isCod: boolean;

  @ApiProperty({ example: `$430` })
  codAmount: number;

  @ApiProperty({
    example: '12 Skid = TOTAL : 12\nNote : please deliver tomorrow at 10 A.M.',
  })
  description: string;

  @ApiProperty({ example: 'Please leave at the door' })
  internalNotes: string;

  @ApiProperty({ example: 591.93 })
  totalPrice: number;

  @ApiProperty({ example: 0 })
  quantity: number;

  @ApiProperty({ example: 10000 })
  weight: number;

  @ApiProperty({ example: '0 x 0 x 0 (L x W x H)' })
  dimensions: string;
}
