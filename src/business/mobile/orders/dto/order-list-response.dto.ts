import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OrderPriority, OrderStatus } from '../domain/order';
import { AddressDto } from '../../../order/orders/dto/address.dto';
import { OrderType } from '../../../../utils/types/order.types';

export class OrderListItemDto {
  @ApiProperty({ example: '550e8400-e29b-41d4-a716-446655440000' })
  id: string;

  @ApiProperty({ example: 'ORD-12345' })
  orderNumber: string;

  @ApiProperty({ example: 'Acme Corp' })
  customerName: string;

  @ApiProperty({ enum: OrderStatus })
  status: OrderStatus;

  @ApiProperty({ enum: OrderPriority })
  priority: OrderPriority;

  @ApiProperty({ example: '123 Main St, Montreal, QC' })
  pickupAddress: string;

  @ApiProperty({ example: '456 Oak St, Toronto, ON' })
  deliveryAddress: string;

  @ApiPropertyOptional({ format: 'date-time' })
  scheduledPickupTime?: Date;

  @ApiPropertyOptional({ format: 'date-time' })
  scheduledDeliveryTime?: Date;

  @ApiProperty({ example: 149.99 })
  totalPrice: number;
}

export class MobileOrderListItemDto {
  @ApiProperty({
    description: 'Order ID',
    example: '550e8400-e29b-41d4-a716-446655450001',
  })
  id: string;

  @ApiProperty({
    description: 'Order number without prefix',
    example: '795599',
  })
  orderNumber: string;

  @ApiProperty({ description: 'Customer name', example: 'Smart Nation' })
  customerName: string;

  @ApiProperty({
    description: 'Order action type',
    example: 'Collection',
  })
  type: OrderType;

  @ApiProperty({
    description: 'Collection location',
    example: '123 Main St, Montreal, QC',
    type: AddressDto,
  })
  collectionLocation: string;

  @ApiProperty({
    description: 'Delivery location',
    example: '456 Oak St, Toronto, ON',
    type: AddressDto,
  })
  deliveryLocation: string;

  @ApiProperty({
    description: 'Delivery type',
    example: 'Sameday',
    enum: ['Sameday', 'Nextday'],
  })
  serviceLevel: 'Sameday' | 'Nextday';

  @ApiProperty({
    description: 'Scheduled collection time',
    example: '10:45 AM',
  })
  scheduledCollectionTime: string;

  @ApiProperty({
    description: 'Scheduled delivery time',
    example: '11:00 AM',
  })
  scheduledDeliveryTime: string;

  @ApiProperty({
    description: 'Order status',
    example: 'In Transit',
    enum: ['In Transit', 'Submitted', 'Assigned', 'Completed'],
  })
  status: string;
}
