import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  Is<PERSON>rray,
  IsBoolean,
  IsDate,
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  <PERSON>Length,
  Min,
  MinDate,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { OrderStatus } from '../domain/order.types';
import { UpdateOrderItemDto } from './update-order-item.dto';

export class UpdateOrderDto {
  @ApiPropertyOptional({
    example: 'REF-12345',
    description: 'Optional reference number (customer supplied)',
  })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  referenceNumber?: string;

  @ApiPropertyOptional({
    enum: OrderStatus,
    description: 'New status of the order',
  })
  @IsEnum(OrderStatus)
  @IsOptional()
  status?: OrderStatus;

  // Package Template Info
  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Package template ID',
  })
  @IsUUID('4')
  @IsOptional()
  packageTemplateId?: string;

  // Collection Information
  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Collection address ID',
  })
  @IsUUID('4')
  @IsOptional()
  collectionAddressId?: string;

  @ApiPropertyOptional({
    example: 'John Smith',
    description: 'Contact name for collection',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  collectionContactName?: string;

  @ApiPropertyOptional({
    example: 'Ring doorbell and wait for security',
    description: 'Special instructions for collection',
  })
  @IsString()
  @IsOptional()
  collectionInstructions?: string;

  @ApiPropertyOptional({
    example: false,
    description: 'Whether signature is required for collection',
  })
  @IsBoolean()
  @IsOptional()
  collectionSignatureRequired?: boolean;

  @ApiPropertyOptional({
    example: '2025-04-10T09:00:00Z',
    description: 'Scheduled collection time',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  scheduledCollectionTime?: Date;

  @ApiPropertyOptional({
    example: '2025-04-10T09:15:30Z',
    description: 'Actual collection time (when picked up)',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  actualCollectionTime?: Date;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Collection zone ID',
  })
  @IsUUID('4')
  @IsOptional()
  collectionZoneId?: string;

  // Delivery Information
  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Delivery address ID',
  })
  @IsUUID('4')
  @IsOptional()
  deliveryAddressId?: string;

  @ApiPropertyOptional({
    example: 'Jane Smith',
    description: 'Contact name for delivery',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  deliveryContactName?: string;

  @ApiPropertyOptional({
    example: 'Leave at front desk if no answer',
    description: 'Special instructions for delivery',
  })
  @IsString()
  @IsOptional()
  deliveryInstructions?: string;

  @ApiPropertyOptional({
    example: true,
    description: 'Whether signature is required for delivery',
  })
  @IsBoolean()
  @IsOptional()
  deliverySignatureRequired?: boolean;

  @ApiPropertyOptional({
    example: '2025-04-10T14:00:00Z',
    description: 'Scheduled delivery time',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  @MinDate(new Date()) // Delivery date must be in the future
  scheduledDeliveryTime?: Date;

  @ApiPropertyOptional({
    example: '2025-04-10T13:45:20Z',
    description: 'Actual delivery time (when delivered)',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  actualDeliveryTime?: Date;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Delivery zone ID',
  })
  @IsUUID('4')
  @IsOptional()
  deliveryZoneId?: string;

  // Vehicle and Assignment Information
  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Vehicle type ID',
  })
  @IsUUID('4')
  @IsOptional()
  vehicleTypeId?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Assigned driver ID',
  })
  @IsUUID('4')
  @IsOptional()
  assignedDriverId?: string;

  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Assigned vehicle ID',
  })
  @IsUUID('4')
  @IsOptional()
  assignedVehicleId?: string;

  @ApiPropertyOptional({
    example: true,
    description: 'Whether cash on delivery is required',
  })
  @IsBoolean()
  @IsOptional()
  isCod?: boolean;

  @ApiPropertyOptional({
    example: 100.0,
    description: 'Cash on delivery amount',
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  codAmount?: number;

  @ApiPropertyOptional({
    example: true,
    description: 'Whether COD has been collected',
  })
  @IsBoolean()
  @IsOptional()
  codCollected?: boolean;

  @ApiPropertyOptional({
    example: '2025-04-10T13:45:20Z',
    description: 'Date/time when COD was collected',
  })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  codCollectionDate?: Date;

  // Pricing Information
  @ApiPropertyOptional({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'Price set ID',
  })
  @IsUUID('4')
  @IsOptional()
  priceSetId?: string;

  @ApiPropertyOptional({
    example: 'Standard',
    description: 'Base price type',
  })
  @IsString()
  @IsOptional()
  @MaxLength(50)
  basePriceType?: string;

  @ApiPropertyOptional({
    example: 45.99,
    description: 'Base price of the order',
  })
  @IsNumber()
  @Min(0)
  @IsOptional()
  basePrice?: number;

  @ApiPropertyOptional({
    example: 15.0,
    description: 'Additional cost for options',
  })
  @IsNumber()
  @IsOptional()
  optionsPrice?: number;

  @ApiPropertyOptional({
    example: 5.0,
    description: 'Miscellaneous price adjustments',
  })
  @IsNumber()
  @IsOptional()
  miscAdjustment?: number;

  @ApiPropertyOptional({
    example: 5.0,
    description: 'Total declared value',
  })
  @IsNumber()
  @IsOptional()
  declaredValue?: number;

  @ApiPropertyOptional({
    example: -10.0,
    description: 'Customer-specific price adjustments',
  })
  @IsNumber()
  @IsOptional()
  customerAdjustment?: number;

  // Additional Fields
  @ApiPropertyOptional({
    example: 'Urgent delivery of medical supplies',
    description: 'Order description',
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    example: 'Customer requested expedited shipping',
    description: 'Comments about the order',
  })
  @IsString()
  @IsOptional()
  comments?: string;

  @ApiPropertyOptional({
    example: 'VIP customer - handle with care',
    description: 'Internal notes (not visible to customer)',
  })
  @IsString()
  @IsOptional()
  internalNotes?: string;

  @ApiPropertyOptional({
    type: [UpdateOrderItemDto],
    description: 'Items to update in this order',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateOrderItemDto)
  @IsOptional()
  items?: UpdateOrderItemDto[];

  @ApiPropertyOptional({
    example: {
      department: 'Electronics',
      priority: 'High',
    },
    description: 'Custom fields',
  })
  @IsObject()
  @IsOptional()
  customFields?: Record<string, any>;

  @ApiPropertyOptional({
    example: {
      originalRequestId: '12345',
      customerSystem: 'ERP-XYZ',
    },
    description: 'Additional metadata',
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
